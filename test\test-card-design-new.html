<!DOCTYPE html>
<html>
<head>
    <title>Card Design Test</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="popup.css">
    <style>
        body {
            width: 450px;
            margin: 20px auto;
            background-color: var(--primary-bg);
        }
        
        .test-container {
            padding: 20px;
        }
        
        .test-title {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Card Design Test</h1>
        
        <!-- Sample conversation items with new design -->
        <div class="conversation-item virtual-item" data-conversation-id="1" role="listitem" tabindex="0">
            <div class="item-header">
                <span class="platform-badge chatgpt">CHATGPT</span>
                <span class="item-title">How to create beautiful CSS cards</span>
                <span class="item-date">17/8/2025 上午2:36:33</span>
                <button class="menu-button" aria-label="More options" title="Options">⋯</button>
            </div>
            <div class="item-preview">
                <strong>You:</strong> I want to create beautiful card designs for my web application. Can you help me with modern CSS techniques including gradients, shadows, and hover effects?
            </div>
        </div>

        <div class="conversation-item virtual-item" data-conversation-id="2" role="listitem" tabindex="0">
            <div class="item-header">
                <span class="platform-badge claude">CLAUDE</span>
                <span class="item-title">JavaScript async/await best practices</span>
                <span class="item-date">17/8/2025 上午1:06:33</span>
                <button class="menu-button" aria-label="More options" title="Options">⋯</button>
            </div>
            <div class="item-preview">
                <strong>You:</strong> What are the best practices for using async/await in JavaScript? I want to handle errors properly and avoid common pitfalls.
            </div>
        </div>

        <div class="conversation-item virtual-item" data-conversation-id="3" role="listitem" tabindex="0">
            <div class="item-header">
                <span class="platform-badge gemini">GEMINI</span>
                <span class="item-title">React component optimization</span>
                <span class="item-date">16/8/2025 下午10:06:33</span>
                <button class="menu-button" aria-label="More options" title="Options">⋯</button>
            </div>
            <div class="item-preview">
                <strong>You:</strong> How can I optimize my React components for better performance? I'm dealing with large lists and frequent re-renders.
            </div>
        </div>
    </div>

    <script>
        // Add click handlers for testing
        document.querySelectorAll('.conversation-item').forEach(item => {
            item.addEventListener('click', () => {
                console.log('Card clicked:', item.dataset.conversationId);
            });
        });

        document.querySelectorAll('.menu-button').forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const title = e.target.closest('.conversation-item').querySelector('.item-title').textContent;
                if (confirm(`Delete "${title}"?`)) {
                    e.target.closest('.conversation-item').remove();
                }
            });
        });
    </script>
</body>
</html>
