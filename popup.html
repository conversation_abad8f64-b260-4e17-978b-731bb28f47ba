<!DOCTYPE html>
<html>
<head>
  <title>LLMLog</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'llm-primary': '#4a90e2',
            'llm-secondary': '#357abd',
            'llm-bg': '#f4f6f8',
            'llm-card': '#ffffff',
            'llm-border': '#e1e8ed',
            'llm-text': '#333333',
            'llm-muted': '#666666'
          },
          fontFamily: {
            'system': ['system-ui', '-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', '"Helvetica Neue"', 'Arial', 'sans-serif']
          }
        }
      }
    }
  </script>
  <link rel="stylesheet" href="popup.css">
  <link rel="stylesheet" href="lib/github-dark.css">
</head>
<body class="font-system bg-llm-bg text-llm-text w-[450px] max-h-[580px] m-0 flex flex-col">
  <a href="#conversation-list" class="skip-link">Skip to main content</a>
  <div id="app" role="application" aria-label="LLMLog Conversation Manager" class="flex flex-col h-full">
    <!-- Main List View -->
    <div id="list-view" role="main" class="flex flex-col h-full">
      <header class="header bg-white border-b border-llm-border sticky top-0 z-10 px-4 py-3">
        <h1 id="app-title" class="text-xl font-bold text-llm-text mb-3">LLMLog</h1>
        <div class="search-bar">
          <label for="search-input" class="sr-only">Search conversations</label>
          <input
            type="search"
            id="search-input"
            placeholder="Search conversations..."
            aria-describedby="search-help"
            role="searchbox"
            aria-expanded="false"
            aria-controls="conversation-list"
            autocomplete="off"
            class="w-full px-4 py-2.5 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:bg-white focus:border-llm-primary focus:ring-2 focus:ring-llm-primary focus:ring-opacity-20 focus:outline-none transition-all duration-200 placeholder-gray-500"
          >
          <div id="search-help" class="sr-only">
            Type to search through your conversation history. Results will appear below.
          </div>
        </div>
      </header>
      <main
        id="conversation-list"
        role="list"
        aria-live="polite"
        aria-label="Conversation list"
        aria-describedby="list-status"
        tabindex="0"
        class="flex-grow overflow-y-auto p-2"
      >
        <!-- Conversation items will be dynamically inserted here -->
        <p class="empty-message text-center text-gray-500 py-10" role="status" aria-live="polite">No conversations recorded yet.</p>
      </main>
      <div id="list-status" class="sr-only" aria-live="polite" aria-atomic="true">
        <!-- Status updates for screen readers -->
      </div>
    </div>

    <!-- Detail View -->
    <div id="detail-view" class="hidden flex flex-col h-full" role="main" aria-labelledby="detail-title">
      <header class="header detail-header bg-white border-b border-llm-border px-4 py-3 flex items-center gap-3">
        <button
          id="back-button"
          class="back-button inline-flex items-center px-3 py-2 text-sm font-medium text-llm-primary hover:text-llm-secondary hover:bg-blue-50 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-llm-primary focus:ring-opacity-20"
          aria-label="Go back to conversation list"
          type="button"
        >&larr; Back</button>
        <h1 id="detail-title" class="text-lg font-semibold text-llm-text">Conversation Detail</h1>
      </header>
      <main id="conversation-detail" role="article" tabindex="0" class="flex-grow overflow-y-auto p-4 bg-white rounded-lg m-2 border border-llm-border">
        <!-- Conversation detail will be dynamically inserted here -->
      </main>
    </div>
  </div>
  <script src="lib/marked.min.js"></script>
  <script src="lib/highlight.min.js"></script>
  <script src="lib/dompurify.min.js"></script>
  <script type="module" src="modules/csp-reporter.js"></script>
  <script src="popup.js"></script>
</body>
</html>