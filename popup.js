// This script will handle the logic for the popup UI.
// - Fetching and displaying conversations
// - Handling search and filter events
// - Showing conversation details

let allConversations = []; // Cache all conversations
let currentView = 'list'; // 'list' or 'detail'
let currentPage = 1;
let currentSearch = '';
let isLoading = false;
let hasMorePages = true;
const PAGE_SIZE = 20;

document.addEventListener('DOMContentLoaded', () => {
  loadConversations();

  // Configure marked to use highlight.js for syntax highlighting
  // Configure marked to use highlight.js for syntax highlighting.
  // This is the recommended, robust way to integrate the two libraries.
  // Configure marked for GitHub Flavored Markdown.
  // We will handle highlighting separately after the content is in the DOM.
  marked.setOptions({
    gfm: true,
    breaks: true
  });

  const searchInput = document.getElementById('search-input');
  // Use optimized debounced search
  const optimizedSearch = DOMOptimizer.debounce((searchTerm) => {
    DOMOptimizer.measurePerformance('Search Operation', () => {
      handleSearch(searchTerm);
    });
  }, 300);

  searchInput.addEventListener('input', (e) => {
    const searchTerm = e.target.value.trim();

    // Show search loading indicator immediately for non-empty searches
    if (searchTerm.length > 0) {
      showSearchLoadingIndicator();
    } else {
      hideSearchLoadingIndicator();
    }

    // Use optimized debounced search
    optimizedSearch(searchTerm.toLowerCase());
  });

  document.getElementById('back-button').addEventListener('click', () => {
    showListView();
  });
});

function loadConversations(reset = true) {
  if (isLoading) return;

  if (reset) {
    currentPage = 1;
    allConversations = [];
    hasMorePages = true;
  }

  if (!hasMorePages) return;

  isLoading = true;
  showLoadingIndicator();

  const payload = {
    page: currentPage,
    limit: PAGE_SIZE,
    search: currentSearch
  };

  chrome.runtime.sendMessage({ namespace: 'database', action: 'getConversations', payload }, (response) => {
    isLoading = false;
    hideLoadingIndicator();

    if (chrome.runtime.lastError) {
      UIFeedback.showErrorMessage(chrome.runtime.lastError.message, 'Failed to load conversations');
      return;
    }

    if (response && response.status === 'success') {
      const newConversations = response.data;
      const pagination = response.pagination;

      if (reset) {
        allConversations = newConversations;
      } else {
        allConversations = [...allConversations, ...newConversations];
      }

      hasMorePages = pagination.hasMore;
      currentPage = pagination.page + 1;

      renderConversations(allConversations, !reset);
      updateLoadMoreButton();

      // Show success message for initial load
      if (reset && allConversations.length > 0) {
        UIFeedback.showInfoMessage(`Loaded ${allConversations.length} conversations`);
      } else if (!reset && newConversations.length > 0) {
        UIFeedback.showInfoMessage(`Loaded ${newConversations.length} more conversations`);
      }
    } else {
      const errorMessage = response ? response.message : "No response received";
      UIFeedback.showErrorMessage(errorMessage, 'Failed to load conversations');
    }
  });
}

function handleSearch(searchTerm) {
  currentSearch = searchTerm;
  hideSearchLoadingIndicator(); // Hide search loading indicator

  if (searchTerm.trim()) {
    UIFeedback.showInfoMessage(`Searching for "${searchTerm}"...`);
  }

  loadConversations(true); // Reset and load with new search
}

function loadMoreConversations() {
  loadConversations(false); // Don't reset, append to existing
}

// Virtual Scrolling Implementation
class VirtualScrollList {
  constructor(container, itemHeight = 80, bufferSize = 5) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.bufferSize = bufferSize;
    this.items = [];
    this.visibleStart = 0;
    this.visibleEnd = 0;
    this.scrollTop = 0;
    this.containerHeight = 0;
    this.totalHeight = 0;

    this.viewport = null;
    this.spacerTop = null;
    this.spacerBottom = null;

    this.init();
  }

  init() {
    // Clear existing content
    this.container.innerHTML = '';

    // Create virtual scroll structure
    this.viewport = document.createElement('div');
    this.viewport.className = 'virtual-scroll-viewport';
    this.viewport.style.cssText = `
      height: 100%;
      overflow-y: auto;
      position: relative;
    `;

    this.spacerTop = document.createElement('div');
    this.spacerTop.className = 'virtual-scroll-spacer-top';

    this.content = document.createElement('div');
    this.content.className = 'virtual-scroll-content';

    this.spacerBottom = document.createElement('div');
    this.spacerBottom.className = 'virtual-scroll-spacer-bottom';

    this.viewport.appendChild(this.spacerTop);
    this.viewport.appendChild(this.content);
    this.viewport.appendChild(this.spacerBottom);
    this.container.appendChild(this.viewport);

    // Add scroll listener
    this.viewport.addEventListener('scroll', () => {
      this.handleScroll();
    });

    // Update container height
    this.updateContainerHeight();
  }

  updateContainerHeight() {
    this.containerHeight = this.viewport.clientHeight;
  }

  setItems(items) {
    this.items = items;
    this.totalHeight = items.length * this.itemHeight;
    this.updateVisibleRange();
    this.render();
  }

  handleScroll() {
    this.scrollTop = this.viewport.scrollTop;
    this.updateVisibleRange();
    this.render();
  }

  updateVisibleRange() {
    if (this.items.length === 0) {
      this.visibleStart = 0;
      this.visibleEnd = 0;
      return;
    }

    const visibleItemCount = Math.ceil(this.containerHeight / this.itemHeight);
    this.visibleStart = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.bufferSize);
    this.visibleEnd = Math.min(this.items.length, this.visibleStart + visibleItemCount + (this.bufferSize * 2));
  }

  render() {
    // Use optimized DOM manipulation
    DOMOptimizer.batchDOMUpdates(() => {
      // Update spacer heights
      this.spacerTop.style.height = `${this.visibleStart * this.itemHeight}px`;
      this.spacerBottom.style.height = `${(this.items.length - this.visibleEnd) * this.itemHeight}px`;

      // Create document fragment for efficient DOM updates
      const fragment = DOMOptimizer.createDocumentFragment();

      // Render visible items
      for (let i = this.visibleStart; i < this.visibleEnd; i++) {
        const item = this.items[i];
        if (item) {
          const element = this.createItemElement(item, i);
          fragment.appendChild(element);
        }
      }

      // Clear content and append all items at once
      this.content.innerHTML = '';
      this.content.appendChild(fragment);
    });
  }

  createItemElement(conversation, index) {
    // Use optimized template-based creation
    const dateString = new Date(conversation.createdAt).toLocaleString();
    const templateData = {
      id: conversation.id,
      index: index,
      platform: conversation.platform,
      platformClass: conversation.platform.toLowerCase(),
      title: escapeHTML(conversation.title),
      date: dateString,
      dateLabel: dateString,
      preview: escapeHTML(conversation.prompt.substring(0, 100)) + '...',
      ariaLabel: `Conversation: ${conversation.title} from ${conversation.platform}, ${dateString}`
    };

    const item = DOMOptimizer.createElementFromTemplate(CONVERSATION_ITEM_TEMPLATE, templateData);

    if (!item) {
      // Fallback to manual creation if template fails
      return this.createItemElementFallback(conversation, index);
    }

    // Set height
    item.style.height = `${this.itemHeight}px`;

    // Add click and keyboard event handlers
    const handleActivation = () => {
      showDetailView(conversation);
    };

    item.addEventListener('click', handleActivation);
    item.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        handleActivation();
      }
    });

    // Add menu button event listener
    const menuButton = item.querySelector('.menu-button');
    if (menuButton) {
      menuButton.addEventListener('click', (e) => {
        e.stopPropagation();
        deleteConversation(conversation.id);
      });
    }

    return item;
  }

  createItemElementFallback(conversation, index) {
    // Fallback method for manual DOM creation
    const item = document.createElement('div');
    item.className = 'conversation-item virtual-item';
    item.setAttribute('data-conversation-id', conversation.id);
    item.setAttribute('data-index', index);
    item.style.height = `${this.itemHeight}px`;

    // Accessibility attributes
    item.setAttribute('role', 'listitem');
    item.setAttribute('tabindex', '0');
    item.setAttribute('aria-label', `Conversation: ${conversation.title} from ${conversation.platform}, ${new Date(conversation.createdAt).toLocaleString()}`);
    item.setAttribute('aria-describedby', `item-preview-${conversation.id}`);

    const dateString = new Date(conversation.createdAt).toLocaleString();
    item.innerHTML = `
      <div class="item-header">
        <span class="platform-badge ${conversation.platform.toLowerCase()}" aria-label="Platform: ${conversation.platform}">${conversation.platform}</span>
        <span class="item-title">${escapeHTML(conversation.title)}</span>
        <span class="item-date" aria-label="Date: ${dateString}">${dateString}</span>
        <button class="menu-button" aria-label="More options" title="Options">⋯</button>
      </div>
      <div class="item-preview" id="item-preview-${conversation.id}">
        <strong>You:</strong> ${escapeHTML(conversation.prompt.substring(0, 100))}...
      </div>
    `;

    // Add menu button event listener
    const menuButton = item.querySelector('.menu-button');
    if (menuButton) {
      menuButton.addEventListener('click', (e) => {
        e.stopPropagation();
        deleteConversation(conversation.id);
      });
    }

    return item;
  }

  scrollToTop() {
    this.viewport.scrollTop = 0;
  }

  destroy() {
    if (this.viewport) {
      this.viewport.removeEventListener('scroll', this.handleScroll);
    }
  }
}

// Global virtual scroll instance
let virtualScrollList = null;

function renderConversations(conversations, append = false) {
  DOMOptimizer.measurePerformance('Render Conversations', () => {
    const listElement = document.getElementById('conversation-list');

    // Initialize virtual scrolling if not already done
    if (!virtualScrollList) {
      virtualScrollList = new VirtualScrollList(listElement, 100, 5);
    }

    if (!conversations || conversations.length === 0) {
      if (!append) {
        listElement.innerHTML = '<p class="empty-message">No conversations recorded yet.</p>';
        virtualScrollList = null;
      }
      return;
    }

    // Update the virtual scroll list
    if (append) {
      // For append mode, we need to merge with existing items
      const existingItems = virtualScrollList ? virtualScrollList.items : [];
      const newItems = [...existingItems];

      conversations.forEach(conv => {
        // Skip if conversation already exists (prevent duplicates)
        if (!newItems.find(item => item.id === conv.id)) {
          newItems.push(conv);
        }
      });

      virtualScrollList.setItems(newItems);
    } else {
      // For reset mode, replace all items
      virtualScrollList.setItems(conversations);
    }

    // Notify accessibility manager
    if (accessibilityManager) {
      accessibilityManager.onConversationsRendered();
    }
  });
}

function showDetailView(conversation) {
  currentView = 'detail';
  document.getElementById('list-view').classList.add('hidden');
  const detailView = document.getElementById('detail-view');
  detailView.classList.remove('hidden');

  document.getElementById('detail-title').textContent = conversation.title;

  const detailElement = document.getElementById('conversation-detail');
  detailElement.innerHTML = `
    <div class="detail-section">
      <div class="detail-meta">
        <strong>Platform:</strong> ${conversation.platform} |
        <strong>Date:</strong> ${new Date(conversation.createdAt).toLocaleString()} |
        <strong>URL:</strong> <a href="${conversation.url}" target="_blank" aria-label="Open conversation URL in new tab">${escapeHTML(conversation.url)}</a>
      </div>
    </div>
    <div class="detail-section">
      <h2>Prompt</h2>
      <div class="detail-content" role="region" aria-label="User prompt">${escapeHTML(conversation.prompt)}</div>
    </div>
    <div class="detail-section">
      <h2>Response</h2>
      <div class="detail-content" role="region" aria-label="AI response">${DOMPurify.sanitize(marked.parse(conversation.response || ''))}</div>
    </div>
  `;

  enhanceCodeBlocks(detailElement);

  // Focus management - focus the detail content for screen readers
  setTimeout(() => {
    detailElement.focus();

    // Announce to screen reader
    if (accessibilityManager) {
      accessibilityManager.announceToScreenReader(`Viewing conversation: ${conversation.title}. Press Escape to go back to list.`);
    }
  }, 100);
}

function enhanceCodeBlocks(container) {
  // Find all code blocks within the container.
  const codeElements = container.querySelectorAll('pre code');

  codeElements.forEach(codeEl => {
    // 1. Highlight the code block.
    // highlightElement will automatically detect the language.
    hljs.highlightElement(codeEl);

    // 2. Add a copy button to the parent <pre> element.
    const preEl = codeEl.parentElement;
    if (preEl.querySelector('.copy-button')) return; // Avoid adding duplicate buttons

    const button = document.createElement('button');
    button.className = 'copy-button';
    button.textContent = 'Copy';
    
    button.addEventListener('click', (e) => {
      e.stopPropagation();
      navigator.clipboard.writeText(codeEl.innerText).then(() => {
        button.textContent = 'Copied!';
        setTimeout(() => {
          button.textContent = 'Copy';
        }, 2000);
      }).catch(err => {
        console.error('Failed to copy code: ', err);
        button.textContent = 'Error';
      });
    });

    preEl.appendChild(button);
  });
}

function showListView() {
  currentView = 'list';
  document.getElementById('detail-view').classList.add('hidden');
  document.getElementById('list-view').classList.remove('hidden');

  // Focus management - return focus to conversation list
  setTimeout(() => {
    const conversationList = document.getElementById('conversation-list');
    conversationList.focus();

    // Announce to screen reader
    if (accessibilityManager) {
      accessibilityManager.announceToScreenReader('Returned to conversation list. Use arrow keys to navigate conversations.');
      accessibilityManager.updateFocusableItems();
    }
  }, 100);
}

function showLoadingIndicator() {
  const listElement = document.getElementById('conversation-list');

  // Handle loading indicator for both virtual scrolling and regular mode
  if (virtualScrollList && virtualScrollList.viewport) {
    let loadingIndicator = virtualScrollList.viewport.querySelector('.loading-indicator');

    if (!loadingIndicator) {
      loadingIndicator = document.createElement('div');
      loadingIndicator.className = 'loading-indicator';
      loadingIndicator.innerHTML = '<p>Loading conversations...</p>';
      loadingIndicator.style.cssText = `
        text-align: center;
        padding: 20px;
        color: var(--text-color);
        position: sticky;
        bottom: 10px;
        background: var(--secondary-bg);
        border-radius: 6px;
        margin: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
      `;
      virtualScrollList.viewport.appendChild(loadingIndicator);
    }
  } else {
    // Fallback for regular mode
    let loadingIndicator = listElement.querySelector('.loading-indicator');

    if (!loadingIndicator) {
      loadingIndicator = document.createElement('div');
      loadingIndicator.className = 'loading-indicator';
      loadingIndicator.innerHTML = '<p>Loading conversations...</p>';
      listElement.appendChild(loadingIndicator);
    }
  }
}

function hideLoadingIndicator() {
  const listElement = document.getElementById('conversation-list');

  // Handle loading indicator for both virtual scrolling and regular mode
  if (virtualScrollList && virtualScrollList.viewport) {
    const loadingIndicator = virtualScrollList.viewport.querySelector('.loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.remove();
    }

    // Reset load more button state for virtual scrolling
    const loadMoreButton = virtualScrollList.viewport.querySelector('.load-more-button');
    if (loadMoreButton) {
      loadMoreButton.innerHTML = '📚 Load More Conversations';
      loadMoreButton.disabled = false;
    }
  } else {
    // Fallback for regular mode
    const loadingIndicator = listElement.querySelector('.loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.remove();
    }

    // Reset load more button state
    const loadMoreButton = listElement.querySelector('.load-more-button');
    if (loadMoreButton) {
      loadMoreButton.innerHTML = '📚 Load More Conversations';
      loadMoreButton.disabled = false;
    }
  }
}

function updateLoadMoreButton() {
  const listElement = document.getElementById('conversation-list');

  // For virtual scrolling, we need to handle the load more button differently
  if (virtualScrollList) {
    // Add load more button to the virtual scroll container
    const viewport = virtualScrollList.viewport;
    let loadMoreButton = viewport.querySelector('.load-more-button');
    let endMessage = viewport.querySelector('.end-message');

    if (hasMorePages && allConversations.length > 0) {
      // Show load more button
      if (!loadMoreButton) {
        loadMoreButton = document.createElement('button');
        loadMoreButton.className = 'load-more-button';
        loadMoreButton.innerHTML = '📚 Load More Conversations';
        // Let CSS handle the styling for better centering
        loadMoreButton.style.display = 'block';
        loadMoreButton.addEventListener('click', () => {
          loadMoreButton.innerHTML = '⏳ Loading...';
          loadMoreButton.disabled = true;
          loadMoreConversations();
        });
        viewport.appendChild(loadMoreButton);
      }
      loadMoreButton.style.display = 'block';
      loadMoreButton.innerHTML = '📚 Load More Conversations';
      loadMoreButton.disabled = false;

      // Hide end message
      if (endMessage) {
        endMessage.style.display = 'none';
      }
    } else if (allConversations.length > 0) {
      // Hide load more button and show end message
      if (loadMoreButton) {
        loadMoreButton.style.display = 'none';
      }

      if (!endMessage) {
        endMessage = document.createElement('div');
        endMessage.className = 'end-message';
        endMessage.innerHTML = '🎉 You\'ve reached the end! All conversations loaded.';
        endMessage.style.cssText = `
          text-align: center;
          padding: 20px;
          color: var(--text-color);
          font-style: italic;
          position: sticky;
          bottom: 10px;
          left: 50%;
          transform: translateX(-50%);
          background: var(--secondary-bg);
          border-radius: 6px;
          margin: 10px auto;
          width: auto;
          max-width: 300px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.05);
          z-index: 10;
        `;
        viewport.appendChild(endMessage);
      }
      endMessage.style.display = 'block';
    } else {
      // Hide both if no conversations
      if (loadMoreButton) {
        loadMoreButton.style.display = 'none';
      }
      if (endMessage) {
        endMessage.style.display = 'none';
      }
    }
  } else {
    // Fallback to original implementation for non-virtual scrolling
    let loadMoreButton = listElement.querySelector('.load-more-button');
    let endMessage = listElement.querySelector('.end-message');

    if (hasMorePages && allConversations.length > 0) {
      // Show load more button
      if (!loadMoreButton) {
        loadMoreButton = document.createElement('button');
        loadMoreButton.className = 'load-more-button';
        loadMoreButton.innerHTML = '📚 Load More Conversations';
        loadMoreButton.addEventListener('click', () => {
          loadMoreButton.innerHTML = '⏳ Loading...';
          loadMoreButton.disabled = true;
          loadMoreConversations();
        });
        listElement.appendChild(loadMoreButton);
      }
      loadMoreButton.style.display = 'block';
      loadMoreButton.innerHTML = '📚 Load More Conversations';
      loadMoreButton.disabled = false;

      // Hide end message
      if (endMessage) {
        endMessage.style.display = 'none';
      }
    } else if (allConversations.length > 0) {
      // Hide load more button and show end message
      if (loadMoreButton) {
        loadMoreButton.style.display = 'none';
      }

      if (!endMessage) {
        endMessage = document.createElement('div');
        endMessage.className = 'end-message';
        endMessage.innerHTML = '🎉 You\'ve reached the end! All conversations loaded.';
        listElement.appendChild(endMessage);
      }
      endMessage.style.display = 'block';
    } else {
      // Hide both if no conversations
      if (loadMoreButton) {
        loadMoreButton.style.display = 'none';
      }
      if (endMessage) {
        endMessage.style.display = 'none';
      }
    }
  }
}

async function deleteConversation(id) {
  // Find the conversation for confirmation
  const conversation = allConversations.find(conv => conv.id == id);
  if (!conversation) {
    UIFeedback.showErrorMessage('Conversation not found');
    return;
  }

  // Show confirmation dialog
  const confirmed = await UIFeedback.showConfirmDialog(
    `Are you sure you want to delete this conversation?\n\n"${conversation.prompt.substring(0, 100)}..."`,
    'Delete Conversation',
    {
      confirmText: 'Delete',
      cancelText: 'Cancel'
    }
  );

  if (!confirmed) {
    return;
  }

  // Show loading state
  UIFeedback.showInfoMessage('Deleting conversation...');

  chrome.runtime.sendMessage({ namespace: 'database', action: 'deleteConversation', payload: { id: id } }, (response) => {
    if (chrome.runtime.lastError) {
      UIFeedback.showErrorMessage(chrome.runtime.lastError.message, 'Failed to delete conversation');
      return;
    }

    if (response && response.status === 'success') {
      // Remove the conversation from the local cache
      allConversations = allConversations.filter(conv => conv.id !== id);

      // Remove the conversation item from the DOM
      const conversationItem = document.querySelector(`[data-conversation-id="${id}"]`);
      if (conversationItem) {
        conversationItem.remove();
      }

      // If we're in detail view for this conversation, go back to list
      if (currentView === 'detail') {
        showListView();
      }

      // If the list is now empty or we have very few items, try to load more
      if (allConversations.length < PAGE_SIZE / 2 && hasMorePages) {
        loadMoreConversations();
      }

      UIFeedback.showSuccessMessage('Conversation deleted successfully');

      // Update accessibility manager
      if (accessibilityManager) {
        accessibilityManager.updateFocusableItems();
      }
    } else {
      const errorMessage = response ? response.message : 'Unknown error occurred';
      UIFeedback.showErrorMessage(errorMessage, 'Failed to delete conversation');
    }
  });
}

function escapeHTML(str) {
    if (!str) return '';
    const div = document.createElement('div');
    div.textContent = str;
    return div.innerHTML;
}

function showSearchLoadingIndicator() {
  const searchBar = document.querySelector('.search-bar');
  let searchLoadingIndicator = searchBar.querySelector('.search-loading-indicator');

  if (!searchLoadingIndicator) {
    searchLoadingIndicator = document.createElement('div');
    searchLoadingIndicator.className = 'search-loading-indicator';
    searchLoadingIndicator.innerHTML = '🔍 Searching...';
    searchBar.appendChild(searchLoadingIndicator);
  }

  searchLoadingIndicator.style.display = 'block';
}

function hideSearchLoadingIndicator() {
  const searchBar = document.querySelector('.search-bar');
  const searchLoadingIndicator = searchBar.querySelector('.search-loading-indicator');
  if (searchLoadingIndicator) {
    searchLoadingIndicator.style.display = 'none';
  }
}

// Virtual scrolling cleanup and resize handling
function cleanupVirtualScrolling() {
  if (virtualScrollList) {
    virtualScrollList.destroy();
    virtualScrollList = null;
  }
}

// Handle window resize for virtual scrolling
function handleResize() {
  if (virtualScrollList) {
    virtualScrollList.updateContainerHeight();
    virtualScrollList.updateVisibleRange();
    virtualScrollList.render();
  }
}

// Accessibility and keyboard navigation
class AccessibilityManager {
  constructor() {
    this.currentFocusIndex = -1;
    this.focusableItems = [];
    this.init();
  }

  init() {
    // Add keyboard event listeners
    document.addEventListener('keydown', this.handleKeyDown.bind(this));

    // Update focusable items when conversations are rendered
    this.updateFocusableItems();
  }

  handleKeyDown(event) {
    const { key, ctrlKey, metaKey } = event;

    // Handle different views
    if (currentView === 'list') {
      this.handleListViewKeyDown(event);
    } else if (currentView === 'detail') {
      this.handleDetailViewKeyDown(event);
    }

    // Global shortcuts
    if (key === 'Escape') {
      if (currentView === 'detail') {
        event.preventDefault();
        this.goBackToList();
      }
    }

    // Search shortcut
    if ((ctrlKey || metaKey) && key === 'f') {
      event.preventDefault();
      this.focusSearch();
    }
  }

  handleListViewKeyDown(event) {
    const { key, shiftKey } = event;

    switch (key) {
      case 'ArrowDown':
        event.preventDefault();
        this.navigateList(1);
        break;

      case 'ArrowUp':
        event.preventDefault();
        this.navigateList(-1);
        break;

      case 'Enter':
      case ' ':
        event.preventDefault();
        this.activateCurrentItem();
        break;

      case 'Delete':
      case 'Backspace':
        if (shiftKey) {
          event.preventDefault();
          this.deleteCurrentItem();
        }
        break;

      case 'Home':
        event.preventDefault();
        this.navigateToFirst();
        break;

      case 'End':
        event.preventDefault();
        this.navigateToLast();
        break;
    }
  }

  handleDetailViewKeyDown(event) {
    const { key } = event;

    switch (key) {
      case 'Escape':
        event.preventDefault();
        this.goBackToList();
        break;
    }
  }

  navigateList(direction) {
    this.updateFocusableItems();

    if (this.focusableItems.length === 0) return;

    this.currentFocusIndex += direction;

    // Wrap around
    if (this.currentFocusIndex >= this.focusableItems.length) {
      this.currentFocusIndex = 0;
    } else if (this.currentFocusIndex < 0) {
      this.currentFocusIndex = this.focusableItems.length - 1;
    }

    this.focusCurrentItem();
    this.announceCurrentItem();
  }

  navigateToFirst() {
    this.updateFocusableItems();
    if (this.focusableItems.length > 0) {
      this.currentFocusIndex = 0;
      this.focusCurrentItem();
      this.announceCurrentItem();
    }
  }

  navigateToLast() {
    this.updateFocusableItems();
    if (this.focusableItems.length > 0) {
      this.currentFocusIndex = this.focusableItems.length - 1;
      this.focusCurrentItem();
      this.announceCurrentItem();
    }
  }

  updateFocusableItems() {
    const listElement = document.getElementById('conversation-list');

    if (virtualScrollList && virtualScrollList.content) {
      this.focusableItems = Array.from(virtualScrollList.content.querySelectorAll('.conversation-item'));
    } else {
      this.focusableItems = Array.from(listElement.querySelectorAll('.conversation-item'));
    }
  }

  focusCurrentItem() {
    if (this.currentFocusIndex >= 0 && this.currentFocusIndex < this.focusableItems.length) {
      const item = this.focusableItems[this.currentFocusIndex];
      item.focus();

      // Scroll into view if needed
      item.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
    }
  }

  activateCurrentItem() {
    if (this.currentFocusIndex >= 0 && this.currentFocusIndex < this.focusableItems.length) {
      const item = this.focusableItems[this.currentFocusIndex];
      item.click();
    }
  }

  deleteCurrentItem() {
    if (this.currentFocusIndex >= 0 && this.currentFocusIndex < this.focusableItems.length) {
      const item = this.focusableItems[this.currentFocusIndex];
      const conversationId = item.getAttribute('data-conversation-id');

      if (conversationId) {
        deleteConversation(conversationId);
      }
    }
  }

  announceCurrentItem() {
    if (this.currentFocusIndex >= 0 && this.currentFocusIndex < this.focusableItems.length) {
      const item = this.focusableItems[this.currentFocusIndex];
      const title = item.querySelector('.item-title')?.textContent || '';
      const platform = item.querySelector('.platform-badge')?.textContent || '';
      const date = item.querySelector('.item-date')?.textContent || '';

      const announcement = `${title} from ${platform}, ${date}. ${this.currentFocusIndex + 1} of ${this.focusableItems.length}`;
      this.announceToScreenReader(announcement);
    }
  }

  announceToScreenReader(message) {
    const statusElement = document.getElementById('list-status');
    if (statusElement) {
      statusElement.textContent = message;
    }
  }

  focusSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
      searchInput.focus();
      searchInput.select();
    }
  }

  goBackToList() {
    const backButton = document.getElementById('back-button');
    if (backButton) {
      backButton.click();
    }
  }

  // Update focus when conversations are rendered
  onConversationsRendered() {
    this.updateFocusableItems();

    // Announce the number of conversations
    const count = this.focusableItems.length;
    if (count > 0) {
      this.announceToScreenReader(`${count} conversations loaded. Use arrow keys to navigate.`);
    }
  }
}

// UI Feedback and Error Handling System
class UIFeedback {
  static showToast(message, type = 'info', duration = 4000) {
    // Remove existing toasts of the same type to prevent duplicates
    const existingToasts = document.querySelectorAll(`.toast.toast-${type}`);
    existingToasts.forEach(toast => toast.remove());

    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    // Create toast content
    const content = document.createElement('div');
    content.className = 'toast-content';
    content.textContent = message;

    // Create close button
    const closeButton = document.createElement('button');
    closeButton.className = 'toast-close';
    closeButton.innerHTML = '×';
    closeButton.setAttribute('aria-label', 'Close notification');
    closeButton.addEventListener('click', () => {
      this.hideToast(toast);
    });

    toast.appendChild(content);
    toast.appendChild(closeButton);

    // Calculate position based on existing toasts
    const existingToastCount = document.querySelectorAll('.toast').length;
    if (existingToastCount > 0) {
      toast.style.bottom = `${20 + (existingToastCount * 80)}px`;
    }

    // Add to DOM
    document.body.appendChild(toast);

    // Trigger animation
    setTimeout(() => {
      toast.classList.add('toast-show');
    }, 10);

    // Auto-hide after duration
    if (duration > 0) {
      setTimeout(() => {
        this.hideToast(toast);
      }, duration);
    }

    return toast;
  }

  static hideToast(toast) {
    if (toast && toast.parentNode) {
      toast.classList.add('toast-hide');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.remove();
        }
      }, 300);
    }
  }

  static showLoadingState(element, message = 'Loading...') {
    if (!element) return;

    element.classList.add('loading-state');
    element.setAttribute('aria-busy', 'true');

    // Store original content
    if (!element.dataset.originalContent) {
      element.dataset.originalContent = element.innerHTML;
    }

    // Add loading indicator
    element.innerHTML = `
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <span class="loading-text">${message}</span>
      </div>
    `;

    // Disable if it's a button
    if (element.tagName === 'BUTTON') {
      element.disabled = true;
    }
  }

  static hideLoadingState(element) {
    if (!element) return;

    element.classList.remove('loading-state');
    element.removeAttribute('aria-busy');

    // Restore original content
    if (element.dataset.originalContent) {
      element.innerHTML = element.dataset.originalContent;
      delete element.dataset.originalContent;
    }

    // Re-enable if it's a button
    if (element.tagName === 'BUTTON') {
      element.disabled = false;
    }
  }

  static showConfirmDialog(message, title = 'Confirm Action', options = {}) {
    return new Promise((resolve) => {
      const dialog = document.createElement('div');
      dialog.className = 'confirm-dialog-overlay';
      dialog.setAttribute('role', 'dialog');
      dialog.setAttribute('aria-modal', 'true');
      dialog.setAttribute('aria-labelledby', 'dialog-title');
      dialog.setAttribute('aria-describedby', 'dialog-message');

      dialog.innerHTML = `
        <div class="confirm-dialog">
          <div class="dialog-header">
            <h3 id="dialog-title">${escapeHTML(title)}</h3>
          </div>
          <div class="dialog-body">
            <p id="dialog-message">${escapeHTML(message)}</p>
          </div>
          <div class="dialog-footer">
            <button class="dialog-button dialog-button-cancel" type="button">
              ${options.cancelText || 'Cancel'}
            </button>
            <button class="dialog-button dialog-button-confirm" type="button">
              ${options.confirmText || 'Confirm'}
            </button>
          </div>
        </div>
      `;

      // Add event listeners
      const cancelButton = dialog.querySelector('.dialog-button-cancel');
      const confirmButton = dialog.querySelector('.dialog-button-confirm');

      const cleanup = () => {
        dialog.remove();
        document.body.classList.remove('dialog-open');
      };

      cancelButton.addEventListener('click', () => {
        cleanup();
        resolve(false);
      });

      confirmButton.addEventListener('click', () => {
        cleanup();
        resolve(true);
      });

      // Close on escape
      const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
          cleanup();
          resolve(false);
          document.removeEventListener('keydown', handleKeyDown);
        }
      };

      document.addEventListener('keydown', handleKeyDown);

      // Close on overlay click
      dialog.addEventListener('click', (e) => {
        if (e.target === dialog) {
          cleanup();
          resolve(false);
        }
      });

      // Add to DOM and show
      document.body.appendChild(dialog);
      document.body.classList.add('dialog-open');

      // Focus the confirm button
      setTimeout(() => {
        confirmButton.focus();
      }, 100);
    });
  }

  static showErrorMessage(error, context = '') {
    let message = 'An unexpected error occurred.';

    if (typeof error === 'string') {
      message = error;
    } else if (error && error.message) {
      message = error.message;
    }

    if (context) {
      message = `${context}: ${message}`;
    }

    console.error('Error:', error);
    this.showToast(message, 'error', 6000);
  }

  static showSuccessMessage(message) {
    this.showToast(message, 'success', 3000);
  }

  static showWarningMessage(message) {
    this.showToast(message, 'warning', 4000);
  }

  static showInfoMessage(message) {
    this.showToast(message, 'info', 3000);
  }
}

// DOM Optimization Utilities
class DOMOptimizer {
  static createDocumentFragment() {
    return document.createDocumentFragment();
  }

  static batchDOMUpdates(callback) {
    // Use requestAnimationFrame for optimal timing
    return new Promise((resolve) => {
      requestAnimationFrame(() => {
        const result = callback();
        resolve(result);
      });
    });
  }

  static createElementFromTemplate(template, data = {}) {
    // Create a temporary container
    const container = document.createElement('div');
    container.innerHTML = template;

    // Replace placeholders with data
    const element = container.firstElementChild;
    if (element && data) {
      this.replacePlaceholders(element, data);
    }

    return element;
  }

  static replacePlaceholders(element, data) {
    // Replace text content placeholders
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node);
    }

    textNodes.forEach(textNode => {
      let content = textNode.textContent;
      Object.keys(data).forEach(key => {
        const placeholder = `{{${key}}}`;
        if (content.includes(placeholder)) {
          content = content.replace(new RegExp(placeholder, 'g'), data[key]);
        }
      });
      textNode.textContent = content;
    });

    // Replace attribute placeholders
    const allElements = element.querySelectorAll('*');
    allElements.forEach(el => {
      Array.from(el.attributes).forEach(attr => {
        let value = attr.value;
        Object.keys(data).forEach(key => {
          const placeholder = `{{${key}}}`;
          if (value.includes(placeholder)) {
            value = value.replace(new RegExp(placeholder, 'g'), data[key]);
          }
        });
        attr.value = value;
      });
    });
  }

  static measurePerformance(name, callback) {
    const startTime = performance.now();
    const result = callback();
    const endTime = performance.now();

    console.log(`${name} took ${(endTime - startTime).toFixed(2)}ms`);
    return result;
  }

  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  static throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  static optimizeImageLoading(img, src, placeholder = null) {
    if (placeholder) {
      img.src = placeholder;
    }

    const tempImg = new Image();
    tempImg.onload = () => {
      img.src = src;
      img.classList.add('loaded');
    };
    tempImg.onerror = () => {
      img.classList.add('error');
    };
    tempImg.src = src;
  }

  static createOptimizedList(container, items, renderItem, options = {}) {
    const {
      batchSize = 50,
      delay = 0
    } = options;

    const fragment = this.createDocumentFragment();
    let currentIndex = 0;

    const renderBatch = () => {
      const endIndex = Math.min(currentIndex + batchSize, items.length);

      for (let i = currentIndex; i < endIndex; i++) {
        const element = renderItem(items[i], i);
        if (element) {
          fragment.appendChild(element);
        }
      }

      currentIndex = endIndex;

      if (currentIndex < items.length) {
        if (delay > 0) {
          setTimeout(renderBatch, delay);
        } else {
          requestAnimationFrame(renderBatch);
        }
      } else {
        // All items rendered, append to container
        container.appendChild(fragment);
      }
    };

    // Start rendering
    renderBatch();
  }

  static observeElementVisibility(element, callback, options = {}) {
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          callback(entry.isIntersecting, entry);
        });
      }, {
        threshold: options.threshold || 0.1,
        rootMargin: options.rootMargin || '0px'
      });

      observer.observe(element);
      return observer;
    } else {
      // Fallback for browsers without IntersectionObserver
      callback(true);
      return null;
    }
  }

  static preloadContent(urls) {
    return Promise.all(urls.map(url => {
      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    }));
  }
}

// Enhanced conversation item template for better performance
const CONVERSATION_ITEM_TEMPLATE = `
  <div class="conversation-item virtual-item"
       role="listitem"
       tabindex="0"
       data-conversation-id="{{id}}"
       data-index="{{index}}"
       aria-label="{{ariaLabel}}"
       aria-describedby="item-preview-{{id}}">
    <div class="item-header">
      <span class="platform-badge {{platformClass}}" aria-label="Platform: {{platform}}">{{platform}}</span>
      <span class="item-title">{{title}}</span>
      <span class="item-date" aria-label="Date: {{dateLabel}}">{{date}}</span>
      <button class="menu-button" aria-label="More options" title="Options">⋯</button>
    </div>
    <div class="item-preview" id="item-preview-{{id}}">
      <strong>You:</strong> {{preview}}
    </div>
  </div>
`;

// Initialize accessibility manager
const accessibilityManager = new AccessibilityManager();

// Add resize listener
window.addEventListener('resize', handleResize);

// Cleanup when popup is closed
window.addEventListener('beforeunload', cleanupVirtualScrolling);